
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\encoding\codepage.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\shared\date_time.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\shared\format.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\shared\formatting.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\shared\generator.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\shared\ids.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\shared\localization_backend.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\shared\message.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\shared\mo_lambda.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\util\codecvt_converter.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\util\default_locale.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\util\info.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\util\locale_data.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\std\codecvt.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\std\collate.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\std\converter.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\std\numeric.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\std\std_backend.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\util\gregorian.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\win32\collate.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\win32\converter.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\win32\lcid.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\win32\numeric.obj" 
"bin.v2\libs\locale\build\msvc-9.0\debug\link-static\threading-multi\win32\win_backend.obj"   