
"bin.v2\libs\log\build\msvc-9.0\release\build-no\link-static\log-api-winnt\threading-multi\parser_utils.obj" 
"bin.v2\libs\log\build\msvc-9.0\release\build-no\link-static\log-api-winnt\threading-multi\init_from_stream.obj" 
"bin.v2\libs\log\build\msvc-9.0\release\build-no\link-static\log-api-winnt\threading-multi\init_from_settings.obj" 
"bin.v2\libs\log\build\msvc-9.0\release\build-no\link-static\log-api-winnt\threading-multi\settings_parser.obj" 
"bin.v2\libs\log\build\msvc-9.0\release\build-no\link-static\log-api-winnt\threading-multi\filter_parser.obj" 
"bin.v2\libs\log\build\msvc-9.0\release\build-no\link-static\log-api-winnt\threading-multi\formatter_parser.obj" 
"bin.v2\libs\log\build\msvc-9.0\release\build-no\link-static\log-api-winnt\threading-multi\default_filter_factory.obj" 
"bin.v2\libs\log\build\msvc-9.0\release\build-no\link-static\log-api-winnt\threading-multi\matches_relation_factory.obj" 
"bin.v2\libs\log\build\msvc-9.0\release\build-no\link-static\log-api-winnt\threading-multi\default_formatter_factory.obj"   