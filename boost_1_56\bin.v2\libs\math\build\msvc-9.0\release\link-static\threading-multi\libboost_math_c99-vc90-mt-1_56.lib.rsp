
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\acosh.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\asinh.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\atanh.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\cbrt.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\copysign.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\erfc.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\erf.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\expm1.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\fmax.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\fmin.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\fpclassify.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\hypot.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\lgamma.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\llround.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\log1p.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\lround.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\nextafter.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\nexttoward.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\round.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\tgamma.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\trunc.obj" 
"bin.v2\libs\math\build\msvc-9.0\release\link-static\threading-multi\pch.obj"   