
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\attribute_name.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\attribute_set.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\attribute_value_set.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\code_conversion.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\core.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\record_ostream.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\severity_level.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\global_logger_storage.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\named_scope.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\process_name.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\process_id.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\thread_id.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\timer.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\exceptions.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\default_attribute_names.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\default_sink.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\text_ostream_backend.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\text_file_backend.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\text_multifile_backend.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\syslog_backend.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\thread_specific.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\once_block.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\timestamp.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\threadsafe_queue.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\event.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\trivial.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\spirit_encoding.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\format_parser.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\date_time_format_parser.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\named_scope_format_parser.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\unhandled_exception_count.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\dump.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\link-static\log-api-winnt\threading-multi\dump_ssse3.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\debug_output_backend.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\light_rw_mutex.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\simple_event_log_res.obj" 
"bin.v2\libs\log\build\msvc-9.0\debug\build-no\link-static\log-api-winnt\threading-multi\event_log_backend.obj"   